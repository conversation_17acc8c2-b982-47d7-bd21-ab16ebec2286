import __config from './config';
import Session from '../common/Session';

export function analysisRes(res) {
  const { errCode, msg, data } = res || {};
  if (errCode !== 0) {
    wx.showToast({
      title: msg,
      icon: 'none',
    });
    return null;
  }
  return data;
}

class Request {
  constructor() {
    Object.assign(this, {
      $$basePath: __config.baseUrl,
    });
    this.__init();
  }

  /**
   * __init
   */
  __init() {
    this.__initDefaults();
    this.__initMethods();
  }

  __initInterceptor() {}
  /**
   * __initDefaults
   */
  __initDefaults() {
    // 方法名后缀字符串
    this.suffix = '';

    // 发起请求所支持的方法
    this.instanceSource = {
      method: ['OPTIONS', 'GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'DELETE', 'TRACE', 'CONNECT'],
    };
  }

  /**
   * 遍历对象构造方法，方法名以小写字母+后缀名
   */
  __initMethods() {
    for (let key in this.instanceSource) {
      this.instanceSource[key].forEach((method, index) => {
        this[method.toLowerCase() + this.suffix] = (...args) => this.__defaultRequest(method, ...args);
      });
    }
  }

  /**
   * 以wx.request作为底层方法
   * @param {String} method 请求方法
   * @param {String} url    接口地址
   * @param {Object} params 请求参数
   * @param {Object} header 设置请求的 header
   * @param {String} dataType 请求的数据类型
   */
  __defaultRequest(method = '', url = '', params = {}, header = {}, dataType = 'json') {
    const $$header = Object.assign({}, this.setHeaders(), header);
    const $$url = this.setUrl(url, params);
    if (params.url) {
      params.url = this.setUrl(params.url, params);
    }

    // 注入拦截器
    const chainInterceptors = (promise, interceptors) => {
      for (let i = 0, ii = interceptors.length; i < ii; ) {
        let thenFn = interceptors[i++];
        let rejectFn = interceptors[i++];
        promise = promise.then(thenFn, rejectFn);
      }
      return promise;
    };

    // 请求参数配置
    const $$config = {
      url: $$url,
      data: params,
      header: $$header,
      method: method,
      dataType: dataType,
    };

    let requestInterceptors = [];
    let responseInterceptors = [];
    let reversedInterceptors = this.setInterceptors();
    let promise = this.__resolve($$config);

    // 缓存拦截器
    reversedInterceptors.forEach((n, i) => {
      if (n.request || n.requestError) {
        requestInterceptors.push(n.request, n.requestError);
      }
      if (n.response || n.responseError) {
        responseInterceptors.unshift(n.response, n.responseError);
      }
    });

    // 注入请求拦截器
    promise = chainInterceptors(promise, requestInterceptors);

    // 发起HTTPS请求
    promise = promise.then(this.__http);

    // 注入响应拦截器
    promise = chainInterceptors(promise, responseInterceptors);

    // 接口调用成功，res = {errCode: number; msg?: string; data?: any}
    promise = promise.then(
      res => res,
      err => err
    );

    return promise;
  }

  /**
   * __http - wx.request
   */
  __http(obj) {
    return new Promise((resolve, reject) => {
      obj.success = res => resolve(res);
      obj.fail = res => reject(res);
      if (__config.debug) {
        console.log('__http', obj);
      }
      wx.request(obj);
    });
  }

  /**
   * __resolve
   */
  __resolve(res) {
    return new Promise((resolve, reject) => {
      resolve(res);
    });
  }

  /**
   * __reject
   */
  __reject(res) {
    return new Promise((resolve, reject) => {
      reject(res);
    });
  }

  getPathValue(obj, desc) {
    var arr = desc.split('.');
    while (arr.length) {
      obj = obj[arr.shift()];
    }
    return obj;
  }

  getRestUrl(url, data) {
    if (!data) {
      return url;
    } else if (data !== null && typeof data === 'object') {
      url = url.replace(/\{\{(.+?)\}\}/g, (_, key) => {
        let name = key.trim();
        return this.getPathValue(data, name);
      });
      url = url.replace(/\{(.+?)\}/g, (_, key) => {
        let name = key.trim();
        return this.getPathValue(data, name);
      });
      return url;
    }
    return url;
  }

  /**
   * 设置请求路径
   */
  setUrl(url, param) {
    let ishttp = /^http(s)?:\/\/.*/i.test(url);
    url = this.getRestUrl(url, param);
    if (ishttp) {
      return url;
    }
    if (url.startsWith('/') || this.$$basePath.endsWith('/')) {
      return `${this.$$basePath}${url}`;
    } else {
      return `${this.$$basePath}/${url}`;
    }
  }

  /**
   * 设置请求的 header , header 中不能设置 Referer
   */
  setHeaders() {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json;charset=UTF-8',
    };
  }

  /**
   * 设置request拦截器
   */
  setInterceptors() {
    this.interceptors = [
      {
        request: request => {
          request.header = request.header || {};
          request.requestTimestamp = new Date().getTime();
          let token = Session.getToken();
          if (token) {
            // 确保token只包含ASCII字符
            try {
              // 如果token包含非ASCII字符，进行URL编码
              if (/[^\x00-\x7F]/.test(token)) {
                console.log('Token包含非ASCII字符，进行URL编码');
                token = encodeURIComponent(token);
              }
              request.header.Authorization = `Bearer ${token}`;
            } catch (error) {
              console.error('Token编码失败:', error);
              request.header.Authorization = `Bearer ${token}`;
            }
          }
          if (request.data.hideLoading) {
            delete request.data.hideLoading;
          } else {
            wx.showLoading({
              title: request.data.loadingText || '加载中...',
            });
          }
          if (request.data['redirecturl']) {
            Session.setRedirecturl(request.data['redirecturl']);
            delete request.data['redirecturl'];
          }
          delete request.data['loadingText'];

          // 调试信息
          console.log('请求拦截器 - 最终请求:', {
            url: request.url,
            method: request.method,
            header: request.header,
            data: request.data
          });

          return request;
        },
        requestError: requestError => {
          console.log('requestError', requestError);
          wx.hideToast();
          return requestError;
        },
        response: response => {
          response.responseTimestamp = new Date().getTime();
          wx.hideLoading();
          if (response.code == 401 || response.status == 401 || response.statusCode == 401) {
            wx.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
            });
            Session.clearUser();
            setTimeout(() => {
              const url = getApp().globalData.homePage;
              console.log('重新登录', url);
              wx.reLaunch({
                url: '/pages/login/index',
              });
            }, 1000);
          }
          var userInfo = Session.getUser();
          const newToken = response.header['new-token'];
          if (userInfo && newToken) {
            userInfo.token = newToken;
            Session.setUser(userInfo);
          }
          return response.data;
        },
        responseError: responseError => {
          console.log('responseError', responseError);
          wx.hideLoading();
          return responseError;
        },
      },
    ];
    this.__initInterceptor();
    return this.interceptors;
  }
}

export default new Request();
