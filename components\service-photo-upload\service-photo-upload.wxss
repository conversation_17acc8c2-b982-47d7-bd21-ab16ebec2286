.service-photo-upload {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.upload-container {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 标题区域 */
.upload-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.upload-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.upload-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 照片容器 */
.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
  min-height: 160rpx;
}

.photo-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.delete-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.photo-index {
  position: absolute;
  bottom: 4rpx;
  left: 4rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.photo-upload,
.photo-uploading {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.photo-upload:active {
  background-color: #f0f0f0;
  border-color: #ff4f8f;
}

.photo-uploading {
  border-color: #ff4f8f;
  background-color: #fff5f8;
}

.upload-icon,
.uploading-icon {
  margin-bottom: 8rpx;
}

.upload-text,
.uploading-text {
  font-size: 40rpx;
  color: #999;
}

.uploading-text {
  color: #ff4f8f;
}

.upload-label,
.uploading-label {
  font-size: 22rpx;
  color: #999;
}

.uploading-label {
  color: #ff4f8f;
}

/* 操作按钮 */
.upload-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background-color: #e8e8e8;
}

.confirm-btn {
  background-color: #ff4f8f;
  color: #fff;
}

.confirm-btn:active {
  background-color: #e6467a;
}
