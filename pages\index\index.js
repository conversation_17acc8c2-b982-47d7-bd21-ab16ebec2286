import dictionaryApi from '../../api/modules/dictionary';
import orderApi from '../../api/modules/order';
import { formatDate } from '../utils/util';
import config from '../../api/config';

Page({
  data: {
    userInfo: null,
    // 订单类型标签
    orderTabs: [],
    currentTab: '', // 当前选中的标签
    orderList: [], // 订单列表
    page: 1, // 当前页码
    pageSize: 50, // 每页数量
    // 接单确认弹窗
    showConfirmModal: false,
    confirmModalTitle: '接单确认',
    confirmModalContent: '确定要接此订单吗？',
    confirmModalBtnText: '确认接单',
    pendingOrderId: '', // 待接单的订单ID
  },

  onLoad() {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      return wx.redirectTo({
        url: '/pages/login/index',
      });
    } else {
      // 加载字典数据
      dictionaryApi.list('服务类型').then(res => {
        this.setData({
          orderTabs: (res || []).map(item => {
            return {
              name: item.name + '单',
              status: item.code,
            };
          }),
          currentTab: (res || [])?.[0]?.code,
        });
        // 加载初始订单数据
        _this.loadOrders();
      });
    }

    // 建立socket连接
    this.connectWebSocket();
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
    });
    this.loadOrders();
  },

  // 加载订单数据
  async loadOrders() {
    wx.showLoading({
      title: '加载中',
    });

    const { id } = this.data.userInfo;
    const res = await orderApi.list(id, this.data.currentTab);
    const newData = res?.list.map(item => {
      return {
        ...item,
        serviceName: item.orderDetails.map(item => item.service.serviceName),
        serviceLogo: item.orderDetails.map(item => item.service.logo)[0],
        orderTime: formatDate(item.orderTime),
        originalPrice: item.originalPrice, // 原价
        totalFee: item.totalFee, // 实付金额
      };
    });

    // 根据当前状态过滤订单
    // const filteredOrders = this.filterOrdersByType(res?.list);

    this.setData({
      orderList: newData,
    });

    wx.hideLoading();
  },

  // 根据状态过滤订单
  filterOrdersByType(orders) {
    return orders.filter(order => order.type === this.data.currentTab);
  },

  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },

  // 加载更多订单
  loadMoreOrders() {
    this.setData({
      page: this.data.page + 1,
    });
    this.loadOrders();
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.orderId;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index`,
    });
  },

  // 打开导航
  openNavigation(e) {
    e.stopPropagation(); // 阻止事件冒泡，避免触发订单详情页面跳转
    const { address, remark } = e.currentTarget.dataset;

    if (!address) {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'none'
      });
      return;
    }

    // 构建完整地址
    const fullAddress = remark ? `${address}(${remark})` : address;

    // 使用腾讯地图API进行地址搜索
    wx.showLoading({
      title: '正在搜索地址...'
    });

    wx.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      data: {
        address: fullAddress,
        key: 'OQRBZ-KZXKF-XEEJJ-YNVQQ-QZJQS-XQFQH' // 腾讯地图API密钥
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.status === 0 && res.data.result && res.data.result.location) {
          const { lat, lng } = res.data.result.location;
          wx.openLocation({
            latitude: lat,
            longitude: lng,
            name: fullAddress,
            address: fullAddress,
            scale: 18
          });
        } else {
          wx.showToast({
            title: '无法找到该地址',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('地址搜索失败:', error);
        wx.showToast({
          title: '导航功能暂时不可用',
          icon: 'none'
        });
      }
    });
  },

  // 删除订单
  deleteOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '删除订单',
      content: '确定要删除此订单吗？',
      success: res => {
        if (res.confirm) {
          // 实际项目中调用删除订单API
          const orderList = this.data.orderList.filter(order => order.orderId !== orderId);
          this.setData({
            orderList,
          });
          wx.showToast({
            title: '删除成功',
            icon: 'success',
          });
        }
      },
    });
  },

  // 催接单
  confirmReceipt(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);},

  // 显示接单确认弹窗
  reschedule(e) {
    const orderId = e.currentTarget.dataset.orderId;
    this.setData({
      showConfirmModal: true,
      pendingOrderId: orderId,
    });
  },

  // 确认接单
  async onConfirmAccept() {
    // 隐藏确认弹窗
    this.setData({
      showConfirmModal: false,
    });

    wx.showLoading({
      title: '接单中',
    });

    const orderId = this.data.pendingOrderId;
    const userId = this.data.userInfo.id;

    try {
      const res = await orderApi.accept(orderId, userId);
      if (res) {
        wx.showToast({
          title: '接单成功',
          icon: 'success',
          // duration: 1000, // 显示1秒
          mask: true, // 防止用户点击
        });

        // 0.5秒后跳转到我的订单页面（给toast足够的显示时间）
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/orders/index',
          });
        }, 500);
      } else {
        wx.showToast({
          title: '接单失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '接单失败',
        icon: 'error',
      });
    } finally {
      // wx.hideLoading();
      this.loadOrders();
    }
  },

  // 取消接单
  onCancelAccept() {
    this.setData({
      showConfirmModal: false,
      pendingOrderId: '',
    });
  },

  // ----------------------------socket管理-----------------------
  // 建立WebSocket连接
  connectWebSocket: function () {
    // 替换为实际的后端WebSocket地址
    const socketUrl = config.socketUrl;

    // 检查是否已有连接
    if (this.data.socketOpen) {
      console.log('Socket已连接，无需重复连接');
      return;
    }

    console.log('开始建立Socket连接:', socketUrl);

    // 只在第一次连接时注册监听器
    if (!this.socketListenersRegistered) {
      this.registerSocketListeners();
      this.socketListenersRegistered = true;
    }

    // 打开WebSocket连接
    wx.connectSocket({
      url: socketUrl,
      success: res => {
        console.log('WebSocket连接成功');
        this.setData({
          socketOpen: true,
        });
        // 重置重连计数
        this.reconnectCount = 0;
      },
      fail: err => {
        console.error('WebSocket连接失败', err);
        this.setData({
          socketOpen: false,
        });
        // 重连逻辑
        this.reconnectWebSocket();
      },
    });
  },

  // 注册Socket监听器（只注册一次）
  registerSocketListeners: function() {
    console.log('注册Socket监听器');

    // 监听WebSocket消息
    wx.onSocketMessage(res => {
      if (this.data.socketOpen) {
        this.handleNotification(res.data);
      }
    });

    // 监听WebSocket关闭
    wx.onSocketClose(res => {
      console.log('WebSocket连接关闭', res);
      this.setData({
        socketOpen: false,
      });
      // 只有在首页时才自动重连
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.reconnectWebSocket();
        }
      }
    });

    // 监听WebSocket错误
    wx.onSocketError(res => {
      console.error('WebSocket连接错误', res);
      this.setData({
        socketOpen: false,
      });
      // 只有在首页时才重连
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.reconnectWebSocket();
        }
      }
    });
  },

  // 处理接收到的通知
  handleNotification: function (message) {
    try {
      const notification = JSON.parse(message);
      console.log('收到新通知', notification);
      switch (notification.type) {
        case 'new_order':
          // 播放通知音效（可选）
          this.playNotificationSound('new');
          // 更新订单列表
          this.loadOrders();
          break;
        case 'cancel_order':
          // 播放通知音效（可选）
          this.playNotificationSound('cancel');
          // 更新订单列表
          this.loadOrders();
          break;
        case 'message':
          // 更新消息列表
          wx.showToast({
            title: notification.message,
            icon: 'none',
            duration: 2000,
          });
          break;
      }
    } catch (error) {
      console.error('解析通知消息失败', error);
    }
  },

  // 播放通知音效
  playNotificationSound: function (type) {
    // 使用小程序的音频API播放通知音效
    const audioContext = wx.createInnerAudioContext();
    if (type === 'new') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message.mp3';
    }
    if (type === 'cancel') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message1.mp3';
    }
    audioContext.autoplay = true;
    audioContext.play();
  },

  // 重连WebSocket
  reconnectWebSocket: function () {
    // 初始化重连计数
    if (!this.reconnectCount) {
      this.reconnectCount = 0;
    }

    // 限制重连次数
    if (this.reconnectCount >= 5) {
      console.log('达到最大重连次数，停止重连');
      return;
    }

    this.reconnectCount++;
    const delay = Math.min(1000 * this.reconnectCount, 10000); // 递增延迟，最大10秒

    console.log(`${delay}ms后进行第${this.reconnectCount}次重连`);

    // 延迟重连，避免频繁连接
    clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      // 再次检查是否在首页
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.connectWebSocket();
        }
      }
    }, delay);
  },

  // 页面显示时检查socket连接
  onShow: function () {
    // 如果socket断开了，重新连接
    if (!this.data.socketOpen) {
      console.log('页面显示时检测到Socket断开，重新连接');
      this.reconnectCount = 0; // 重置重连计数
      this.connectWebSocket();
    }
  },

  // 页面隐藏时保持连接（不断开）
  onHide: function () {
    console.log('首页隐藏，保持Socket连接');
    // 清除重连定时器，避免在后台重连
    clearTimeout(this.reconnectTimer);
  },

  // 页面卸载时关闭连接
  onUnload: function () {
    console.log('首页卸载，关闭Socket连接');
    clearTimeout(this.reconnectTimer);
    if (this.data.socketOpen) {
      wx.closeSocket();
      this.setData({
        socketOpen: false,
      });
    }
  },
});
