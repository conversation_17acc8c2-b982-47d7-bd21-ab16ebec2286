import { formatNormalDate } from '../../utils/util';
import orderApi from '../../../api/modules/order';
import reviewApi from '../../../api/modules/review';
import specialNoteApi from '../../../api/modules/specialNote';
import Session from '../../../common/Session';

Page({
  data: {
    orderDetail: {}, // 订单
    showMoreActions: false,
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    userInfo: null, // 用户信息
    // 追加服务相关
    pendingAdditionalServices: [], // 待确认的追加服务列表
    allAdditionalServices: [], // 所有追加服务列表
    showRejectModal: false, // 是否显示拒绝原因输入框
    rejectReason: '', // 拒绝原因
    currentAdditionalService: null, // 当前操作的追加服务
    // 特殊情况说明相关
    specialNoteData: null, // 特殊情况说明数据
    showSpecialNote: false, // 是否显示特殊情况说明组件
    specialNoteReadonly: false, // 特殊情况说明是否为只读模式
  },

  onLoad(options) {
    // 获取用户信息
    const userInfo = Session.getUser();
    console.log('订单详情页获取到的用户信息:', userInfo);
    this.setData({ userInfo });
    this.loadOrderDetail(options.orderId);
  },

  onShow() {
    // 页面显示时刷新数据（从支付页面返回时会触发）
    if (this.data.orderDetail && this.data.orderDetail.id) {
      console.log('页面重新显示，刷新订单数据');
      this.loadOrderDetail(this.data.orderDetail.id);
    }
  },

  // 设置订单详情数据
  setOrderDetail(info) {
    // 使用从订单列表传递过来的追加服务信息
    const pendingAdditionalServices = info.pendingAdditionalServices || [];

    this.setData({
      orderDetail: {
        ...info,
        additionalServices:
          info.orderDetails?.flatMap(detail => detail.additionalServices?.map(v => v.name) || []) || [],
        petName: info.orderDetails?.map(item => item.petName)[0] || '',
        serviceTime: info.serviceTime ? formatNormalDate(info.serviceTime) : null,
        // 下单时间
        orderTime: info.orderTime ? formatNormalDate(info.orderTime) : null,
        createdAt: info.createdAt ? formatNormalDate(info.createdAt) : null,
        originalPrice: info.originalPrice, // 原价
        totalFee: info.totalFee, // 实付金额
      },
      pendingAdditionalServices: pendingAdditionalServices.map(item => ({
        ...item,
        createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
      })),
    });

    // 如果订单有追加服务，则加载所有追加服务
    if (info.hasAdditionalServices && info.orderDetails && info.orderDetails.length > 0) {
      this.loadAllAdditionalServices(info.orderDetails[0].id);
    }

    // 加载特殊情况说明
    this.loadSpecialNote(info.id);
  },

  // 加载所有追加服务
  async loadAllAdditionalServices(orderDetailId) {
    try {
      const res = await orderApi.getAdditionalServices(orderDetailId);
      if (res && Array.isArray(res)) {
        // 格式化追加服务数据
        const formattedServices = res.map(item => ({
          ...item,
          createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
          confirmTime: item.confirmTime ? formatNormalDate(item.confirmTime) : null,
          statusText: this.getAdditionalServiceStatusText(item.status),
        }));

        // 分离待确认和其他状态的追加服务
        const pendingServices = formattedServices.filter(item => item.status === 'pending_confirm');
        const otherServices = formattedServices.filter(item => item.status !== 'pending_confirm');

        this.setData({
          pendingAdditionalServices: pendingServices,
          allAdditionalServices: otherServices, // 只显示非待确认状态的服务
        });
      }
    } catch (error) {
      console.error('加载追加服务失败:', error);
    }
  },

  // 获取追加服务状态文字
  getAdditionalServiceStatusText(status) {
    const statusMap = {
      pending_confirm: '待确认',
      confirmed: '已确认',
      rejected: '已拒绝',
      pending_payment: '待付款',
      paid: '已付款',
      completed: '已完成',
      cancelled: '已取消',
      refunding: '退款中',
      refunded: '已退款',
    };
    return statusMap[status] || status;
  },

  // 通过API加载订单详情
  async loadOrderDetail() {
    wx.showLoading({ title: '加载中...' });
    try {
      // 首先尝试从本地存储获取（订单列表页面已经存储了数据）
      const info = wx.getStorageSync('orderInfo');
      console.log('info: ', info);
      if (info) {
        const formattedOrder = {
          ...info,
          orderId: info.id,
          orderNumber: info.sn,
          status: info.status,
          statusText: info.status,
          productName: info.orderDetails?.[0].service.serviceName,
          productImage: info.orderDetails?.[0].service.logo,
          petName: info.orderDetails?.[0].petName,
          userAdress: info.addressDetail + '(' + info.addressRemark + ')',
          quantity: 1,
          expectTime: formatNormalDate(info.serviceTime),
          serviceTime: info.serviceTime,
          extraServive: (info.orderDetails?.[0].additionalServices || []).map(v => v.name),
        };
        this.setOrderDetail(formattedOrder);
        return;
      }

      wx.showToast({
        title: '获取订单信息失败',
        icon: 'none',
      });
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },
  // 切换更多操作弹窗
  toggleOrderActions() {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 提交订单
  submitOrder() {
    // 验证表单信息
    if (!this.validateForm()) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    // 模拟订单提交
    wx.showLoading({
      title: '提交订单中',
    });

    // 这里应该调用后端API提交订单
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 跳转到订单列表或详情页
          wx.navigateTo({
            url: '/pages/orderList/orderList',
          });
        },
      });
    }, 1500);
  },

  // 取消订单
  cancelOrder() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单验证
  validateForm() {
    const { customerName, customerPhone, customerAddress, quantity } = this.data;
    return customerName && customerPhone && customerAddress && quantity > 0;
  },

  // 联系客户
  contactCustomer() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能联系客户',
        icon: 'none',
      });
      return;
    }

    const phoneNumber = orderDetail.customer?.phone || orderDetail.customer?.mobile;

    if (!phoneNumber) {
      wx.showToast({
        title: '客户手机号不存在',
        icon: 'none',
      });
      return;
    }

    wx.showModal({
      title: '联系客户',
      content: `确定要拨打客户电话 ${phoneNumber} 吗？`,
      success: res => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: err => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none',
              });
            },
          });
        }
      },
    });
  },

  // 修改上门时间
  reschedule() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能修改上门时间',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showTimePicker: true,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.orderDetail.orderId || this.data.orderDetail.id,
        this.data.userInfo.id,
        new Date(this.data.selectedTime).toISOString()
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...this.data.orderDetail,
          serviceTime: formatNormalDate(this.data.selectedTime),
        };
        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      selectedTime: '',
    });
  },

  // 查看评价
  async viewReview() {
    const orderId = this.data.orderDetail.orderId || this.data.orderDetail.id;

    wx.showLoading({
      title: '加载中...',
    });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 将评价信息存储到本地存储，供评价详情页使用
        wx.setStorageSync('reviewInfo', reviewData);
        wx.navigateTo({
          url: `/pages/orders/reviewDetail/index?orderId=${orderId}`,
        });
      } else {
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取评价信息失败:', error);
      wx.showToast({
        title: '获取评价失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 确认追加服务
  confirmAdditionalService(e) {
    const service = e.currentTarget.dataset.service;
    const serviceName = service.details?.[0]?.serviceName || service.serviceName || '追加服务';

    wx.showModal({
      title: '确认追加服务',
      content: `确定要确认客户申请的"${serviceName}"追加服务吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.confirmAdditionalService(
              service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
              service.id,
              this.data.userInfo.id
            );

            if (result) {
              wx.showToast({
                title: '确认成功',
                icon: 'success',
              });

              // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
              if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
                this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
              }

              // 通知订单列表页面刷新数据
              this.notifyOrderListRefresh();
            } else {
              wx.showToast({
                title: '确认失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('确认追加服务失败:', error);
            wx.showToast({
              title: '确认失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 拒绝追加服务
  rejectAdditionalService(e) {
    const service = e.currentTarget.dataset.service;
    this.setData({
      currentAdditionalService: service,
      showRejectModal: true,
      rejectReason: '',
    });
  },

  // 输入拒绝原因
  onRejectReasonInput(e) {
    this.setData({
      rejectReason: e.detail.value,
    });
  },

  // 确认拒绝
  async confirmReject() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    try {
      const service = this.data.currentAdditionalService;
      const result = await orderApi.rejectAdditionalService(
        service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
        service.id,
        this.data.userInfo.id,
        this.data.rejectReason
      );

      if (result) {
        wx.showToast({
          title: '已拒绝',
          icon: 'success',
        });

        // 关闭拒绝模态框
        this.setData({
          showRejectModal: false,
          currentAdditionalService: null,
          rejectReason: '',
        });

        // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
        if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
          this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
        }

        // 通知订单列表页面刷新数据
        this.notifyOrderListRefresh();
      } else {
        wx.showToast({
          title: '拒绝失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('拒绝追加服务失败:', error);
      wx.showToast({
        title: '拒绝失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 取消拒绝
  cancelReject() {
    this.setData({
      showRejectModal: false,
      currentAdditionalService: null,
      rejectReason: '',
    });
  },



  // 通知订单列表页面刷新数据
  notifyOrderListRefresh() {
    // 获取页面栈中的订单列表页面
    const pages = getCurrentPages();
    const orderListPage = pages.find(page => page.route === 'pages/orders/index');

    if (orderListPage && orderListPage.resetAndReload) {
      // 调用订单列表页面的刷新方法
      orderListPage.resetAndReload();
    } else {
      // 如果找不到订单列表页面，使用事件总线通知
      getApp().globalData.needRefreshOrderList = true;
    }
  },

  // 特殊情况说明相关方法

  // 加载特殊情况说明
  async loadSpecialNote(orderId) {
    try {
      const noteData = await specialNoteApi.get(orderId);
      this.setData({
        specialNoteData: noteData,
      });
    } catch (error) {
      console.log('获取特殊情况说明失败或不存在:', error);
      this.setData({
        specialNoteData: null,
      });
    }
  },

  // 显示特殊情况说明弹窗
  showSpecialNote() {
    const { orderDetail } = this.data;

    // 根据订单状态判断是否为只读模式
    const readonly = orderDetail.status === '已完成' || orderDetail.status === '已评价';

    this.setData({
      showSpecialNote: true,
      specialNoteReadonly: readonly,
    });
  },

  // 特殊情况说明确认提交
  async onSpecialNoteConfirm(e) {
    const { content, photoList } = e.detail;
    const { orderDetail, specialNoteData, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '保存中...',
    });

    try {
      let result;
      if (specialNoteData) {
        // 更新已有的特殊情况说明
        result = await specialNoteApi.update(orderId, employeeId, content, photoList);
      } else {
        // 创建新的特殊情况说明
        result = await specialNoteApi.create(orderId, employeeId, content, photoList);
      }

      if (result) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('保存特殊情况说明失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 特殊情况说明取消
  onSpecialNoteCancel() {
    this.setData({
      showSpecialNote: false,
      specialNoteReadonly: false,
    });
  },

  // 删除特殊情况说明
  async onSpecialNoteDelete() {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '删除中...',
    });

    try {
      const result = await specialNoteApi.delete(orderId, employeeId);
      if (result) {
        wx.showToast({
          title: '删除成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('删除特殊情况说明失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 预览特殊情况说明图片
  previewSpecialNotePhoto(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    wx.previewImage({
      current: url,
      urls: urls
    });
  },
});
